<template>
  <uiModal
    modal-name="confirmActionModal"
    :title="t('confirmActionModal.title')"
    :actions="[
      { value: 'close', name: t('errorMessageModal.closeButton') },
      { value: 'confirm', name: t('confirmActionModal.button') },
    ]"
    :open="isEditUserModalShown"
    type="default"
    @modalAction="handleModalAction"
  >
    <p class="text-sm text-gray-500 mb-4">
      {{ t("confirmActionModal.users") }}
    </p>
  </uiModal>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { ref } from "vue";

const isEditUserModalShown = ref(false);

const handleModalAction = (action: any) => {
  emits("confirmActionModal", {
    modal: "confirmActionModal",
    action,
  });
};

const emits = defineEmits(["confirmActionModal"]);
</script>
