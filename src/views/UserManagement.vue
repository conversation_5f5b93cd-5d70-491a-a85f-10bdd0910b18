<template>
  <EditUserModal
    modal-name="editUsersModal"
    :open="isEditUserModalShown"
    :items="selectedUsers"
    @brandsEdited="brands = $event"
    @rolesEdited="role = $event"
    @editUsersModal="editUsersFunction($event)"
  />
  <ConfirmActionModal
    modal-name="confirmActionModal"
    :open="isConfirmActionModal"
    @modal-action="confirModalActions($event)"
  />
  <TableWrapper
    :header="table.header"
    :ordered_by="table.orderedBy"
    :order_direction="table.orderDirection"
    :items="formattedItems"
    :filters="table.filters"
    :last_page="last_page"
    :checkbox="true"
    :create-button-icon="PlusIcon"
    :create-button-text="t('filterTable.createButton')"
    @filters-updated="handleFiltersUpdated"
    @btn-action="handleCreateButton('create')"
    @edit="handleAction"
    @delete="handleAction"
    @back-to-user-management="handleNoResults('back-to-user-management')"
    @home-to-accounts="handleNoResults('home-to-accounts')"
    @home="handleNoResults('home')"
  >
  </TableWrapper>
</template>

<script setup lang="ts">
import TableWrapper from "@/components/TableWrapper.vue";
import { storeToRefs } from "pinia";
import { computed, ref } from "vue";
import { useUserListStore } from "@/stores/userList";
import EditUserModal from "./UserEditModal.vue";
import ConfirmActionModal from "./ConfirmActionModal.vue";
import { t } from "@/locales";
import type {
  SearchFilters,
  ErrorObject,
  ErrorMessageObject,
  FormattedData,
} from "@/types";
import dayjs from "dayjs";
import type { TableActionEvent, EditUserModalAction } from "@/types";
import { type uiModalEvent } from "@/uiTypes";
import { useNotificationStore } from "@/stores/notification";
import router from "@/router";
import { PlusIcon } from "@heroicons/vue/24/outline";
const userListStore = useUserListStore();
const { last_page } = storeToRefs(userListStore);
const isConfirmActionModal = ref(false);
const isEditUserModalShown = ref(false);
const selectedUsers = ref<number[]>([]);
const brands = ref({
  name: "",
  value: "",
});
const role = ref({
  name: "",
  id: "",
});
const notificationStore = useNotificationStore();

const handleFiltersUpdated = async (filters: SearchFilters) => {
  return await userListStore.getUsers(filters, userListStore.$state);
};

const handleCreateButton = (action: string) => {
  switch (action) {
    case "create":
      router.push({ name: "createUser" });
      break;
    default:
      console.log("Unhandled action:", action);
  }
};

const formattedItems = computed((): FormattedData[] | null => {
  if (!userListStore.filteredItems) {
    return null;
  }

  return userListStore.filteredItems.map((userList, i) => {
    const {
      id,
      email,
      brand_ids,
      mfa_required,
      group,
      created_at,
      updated_at,
    } = userList;
    const newRow: (string | ErrorObject | ErrorMessageObject)[] = [
      id ? id : "-",
      email ? email : "-",
      brand_ids ? brand_ids : "-",
      group ? group : "-",
      getMfaStatus(mfa_required === "true"),
      created_at ? dayjs(created_at).format("DD/MM/YYYY HH:mm A") : "-",
      updated_at ? dayjs(updated_at).format("DD/MM/YYYY HH:mm A") : "-",
    ];
    return {
      id: i + 1,
      row: newRow,
    };
  });
});

const getMfaStatus = (mfa: Boolean) => {
  const content = mfa ? t("common.yes") : t("common.no");
  const color = mfa ? "success" : "warning";

  return {
    content,
    color,
    type: "tag",
  };
};

const handleAction = (e: TableActionEvent) => {
  if (e.action === "delete" || e.action === "edit") {
    const userIdexesToBeModified = e.items.map(
      (position: number) => position - 1
    );
    selectedUsers.value = [...userIdexesToBeModified];
    if (e.action === "delete") {
      isConfirmActionModal.value = true;
    } else if (e.action === "edit") {
      isEditUserModalShown.value = true;
    }
  }
};

const editUsersFunction = async (event: EditUserModalAction): Promise<void> => {
  switch (event.action) {
    case "close":
      isEditUserModalShown.value = false;
      break;
    case "edit": {
      const areUsersEdited = await userListStore.editUsers(
        selectedUsers.value,
        event.selectedGroup.id,
        event.selectedBrands,
        event.selectedMfaRequired
      );
      if (areUsersEdited) {
        notificationStore.success(
          t("editUserModal.editUserSuccess.title"),
          t("editUserModal.editUserSuccess.body")
        );
      }
      isEditUserModalShown.value = false;
      selectedUsers.value = [];
      userListStore.getUsers(
        userListStore.$state.filters,
        userListStore.$state
      );
      break;
    }
    default:
      return;
  }
};

const confirModalActions = async (event: uiModalEvent): Promise<void> => {
  switch (event.modal) {
    case "confirmActionModal":
      switch (event.action) {
        case "close":
          isConfirmActionModal.value = false;
          break;
        case "confirm": {
          const areUsersDeleted = await userListStore.deleteUsers(
            selectedUsers.value
          );
          if (areUsersDeleted) {
            notificationStore.success(
              t("confirmActionModal.deleteUserSuccess.title"),
              t("confirmActionModal.deleteUserSuccess.body")
            );
          }
          isConfirmActionModal.value = false;
          selectedUsers.value = [];
          userListStore.getUsers(
            userListStore.$state.filters,
            userListStore.$state
          );
          break;
        }
        default:
          console.log("Unhandled action:", event.action);
      }
      break;
    default:
      return;
  }
};

const handleNoResults = (action: string) => {
  if (action === "home") {
    router.push({ name: "accounts" });
  }
};

const table = {
  orderedBy: "id",
  orderDirection: "desc",
  header: [
    { name: t("filterTable.userId"), value: "id" },
    { name: t("common.email"), value: "email" },
    { name: t("filterTable.brandIds"), value: "brand_ids" },
    { name: t("filterTable.group"), value: "group" },
    { name: t("filterTable.mfa"), value: "" },
    { name: t("filterTable.tableHeaderCreatedAt"), value: "" },
    { name: t("filterTable.tableHeaderUpdatedAt"), value: "" },
  ],
  filters: [
    { name: t("filterTable.userId"), value: "id" },
    { name: t("common.email"), value: "email" },
    { name: t("filterTable.brandIds"), value: "brand_ids" },
    { name: t("filterTable.group"), value: "group" },
  ],
};

defineExpose({ formattedItems });
</script>
