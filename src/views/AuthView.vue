<template>
  <div
    class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8"
  >
    <uiModal
      modal-name="resetPasswordModal"
      :title="t('auth.resetPassword.header')"
      :actions="[
        { value: 'forgotPassword', name: t('auth.resetPassword.resetButton') },
      ]"
      :open="openResetPasswordModal"
      @modal-action="modalActions($event)"
    >
      <p class="mb-4">{{ t("auth.resetPassword.body") }}</p>
      <uiInput
        v-model="state.email"
        :label="t('common.email')"
        :loading="loading"
        name="email"
        type="email"
        :color="v$.email.$errors[0]?.$uid ? 'red' : ''"
        :error="v$.email.$errors[0]?.$uid ? t('auth.emailInputError') : ''"
      />
    </uiModal>

    <uiModal
      modal-name="newPasswordRequiredModal"
      :title="t('auth.newPassword.header')"
      :actions="[
        {
          value: 'newPasswordRequired',
          name: t('auth.newPassword.resetButton'),
        },
      ]"
      :open="openNewPasswordModal"
      @modal-action="modalActions($event)"
    >
      <p class="mb-4">{{ t("auth.newPassword.body") }}</p>
      <uiInput
        :value="state.password"
        :label="t('common.password')"
        :loading="loading"
        name="password"
        type="password"
        :color="v$.password.$errors[0]?.$uid ? 'danger' : ''"
        :error="
          v$.password.$errors[0]?.$uid ? t('auth.passwordInputError') : ''
        "
        @input-changed="state.password = $event.value"
      />
    </uiModal>

    <uiModal
      modal-name="mfaModal"
      :title="t('auth.requireVerification')"
      :actions="[
        { value: 'handleMfaCode', name: t('confirmActionModal.button') },
      ]"
      :open="openMfaModal"
      @modal-action="modalActions($event)"
    >
      <p class="mb-4">{{ t("mfaConfigure.codeLabel") }}</p>
      <uiInput
        :label="t('common.code')"
        :loading="false"
        name="info"
        placeholder="..."
        type="number"
        @input-changed="validationCode = $event.value"
      />
    </uiModal>

    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <img
        class="mx-auto w-auto"
        :src="HOTELINKING_AUTOCHECKIN_LOGO"
        alt="Autocheckin logo"
      />
      <h2
        class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"
      >
        {{ t("auth.header") }}
      </h2>
    </div>
    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
      <form class="space-y-6" action="#" method="POST">
        <uiInput
          :value="state.email"
          :loading="loading"
          :label="t('common.email')"
          :color="v$.email.$errors[0]?.$uid ? 'danger' : ''"
          :error="v$.email.$errors[0]?.$uid ? t('auth.emailInputError') : ''"
          data-test="emailInput"
          name="email"
          type="email"
          required
          @input-changed="state.email = $event.value"
        />
        <uiInput
          :value="state.password"
          :loading="loading"
          :label="t('common.password')"
          :color="v$.password.$errors[0]?.$uid ? 'danger' : ''"
          :error="
            v$.password.$errors[0]?.$uid ? t('auth.passwordInputError') : ''
          "
          data-test="passwordInput"
          name="password"
          type="password"
          required
          @input-changed="state.password = $event.value"
        />
        <div class="flex flex-col sm:flex-row justify-between items-center">
          <uiButton data-test="logInButton" :loading="loading" @click="submit"
            >{{ t("auth.logInButton") }}
          </uiButton>
          <p class="mt-4 sm:mt-0 text-sm text-gray-500">
            {{ t("auth.forgotPassword") }}
            <span
              data-test="resetPasswordButton"
              class="text-lime-400 hover:text-indigo-900 cursor-pointer font-bold"
              @click="openResetPasswordModal = true"
              >{{ t("auth.resetPasswordButton") }}</span
            >
          </p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HOTELINKING_AUTOCHECKIN_LOGO } from "@/constants";
import { ref } from "vue";
import { reactive, computed } from "vue";
import { useVuelidate } from "@vuelidate/core";
import { email, required } from "@vuelidate/validators";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { useNotificationStore } from "@/stores/notification";
import { t } from "@/locales";
import { goToBrands } from "@/helpers/router";
import { type uiModalEvent } from "@/uiTypes";

const loading = ref(false);
const openResetPasswordModal = ref(false);
const openNewPasswordModal = ref(false);
const openMfaModal = ref(false);
const validationCode = ref("");
const LOGIN_RECEPTION_URL = `${import.meta.env.VITE_APP_RECEPTION_URL}`;

const userStore = useUserStore();
const notificationStore = useNotificationStore();

const router = useRouter();

const modalActions = (event: uiModalEvent): void => {
  if (event.action === "close" && event.modal === "resetPasswordModal") {
    openResetPasswordModal.value = false;
  }

  if (event.action === "close" && event.modal === "newPasswordRequiredModal") {
    openNewPasswordModal.value = false;
  }

  if (event.action === "close" && event.modal === "mfaModal") {
    openMfaModal.value = false;
  }

  if (event.action === "forgotPassword") {
    forgotPassword();
  }

  if (event.action === "newPasswordRequired") {
    newPasswordRequired();
  }

  if (event.action === "handleMfaCode") {
    handleMfaCode();
  }
};

//Credentials from login form initialized as empty
const state = reactive({
  email: "",
  password: "",
});

//Validation rules
const rules = computed(() => {
  return {
    email: { required, email },
    password: { required },
  };
});

const v$ = useVuelidate(rules, state);

//Check user is logged in already
if (userStore.isAuthenticated) {
  goToBrands();
}

async function submit(): Promise<void> {
  const valid = await v$.value.$validate();
  if (!valid) return;
  await userStore.login({
    email: state.email,
    password: state.password,
  });

  if (userStore.error) {
    switch (userStore.error) {
      case "NewPasswordRequiredException":
        userStore.setEmail(state.email);
        openNewPasswordModal.value = true;
        break;
      case "PasswordResetRequiredException":
        // user needs to create a new password following the same steps as the reset
        openResetPasswordModal.value = true;
        break;
      case "NotAuthorizedException":
        notificationStore.error(
          t("auth.identificationError.title"),
          t("auth.identificationError.body")
        );
        break;
      case "UserNotFoundException":
        notificationStore.error(
          t("auth.userNotFoundError.title"),
          t("auth.userNotFoundError.body")
        );
        break;
      case "SoftwareTokenMfaException":
        openMfaModal.value = true;
        break;
      default:
        notificationStore.error(
          t("auth.defaultError.title"),
          t("auth.defaultError.body")
        );
    }
  } else {
    if (userStore.needMfaSetup) {
      router.push({ name: "MfaConfigure" });
      return;
    }

    if (userStore.isAuthenticated) {
      notificationStore.success(t("auth.isAuth.title"), t("auth.isAuth.body"));
    }
    if (userStore.isReception) {
      window.location.href = LOGIN_RECEPTION_URL;
    }
    goToBrands();
  }
}

async function forgotPassword(): Promise<void> {
  const emailValid = await v$.value.email.$validate();
  if (!emailValid) return;
  await userStore.forgotPassword(state.email);
  if (userStore.error) {
    switch (userStore.error) {
      case "UserNotFoundException":
        notificationStore.error(
          t("auth.userNotFoundError.title"),
          t("auth.userNotFoundError.body")
        );
        break;
      case "LimitExceededException":
        notificationStore.error(
          t("newPassword.attemptsError.title"),
          t("newPassword.attemptsError.body")
        );
        break;
      default:
        notificationStore.error(
          t("auth.defaultError.title"),
          t("auth.defaultError.body")
        );
    }
  } else {
    userStore.setEmail(state.email);
    router.push({ name: "newPassword" });
  }
}

async function newPasswordRequired(): Promise<void> {
  await userStore.completeNewPassword(state.password);
  if (userStore.error) {
    notificationStore.error(
      t("auth.defaultError.title"),
      t("auth.defaultError.body")
    );
  } else {
    userStore.needMfaSetup
      ? router.push({ name: "MfaConfigure" })
      : goToBrands();
    return;
  }
}

async function handleMfaCode(): Promise<void> {
  const isValidateCode = await userStore.validateMfaCode(validationCode.value);

  if (isValidateCode) {
    notificationStore.success(t("auth.isAuth.title"), t("auth.isAuth.body"));
    goToBrands();

    return;
  }

  notificationStore.error(
    t("mfaConfigure.notification.error.title"),
    t("mfaConfigure.notification.error.message")
  );
}
</script>
