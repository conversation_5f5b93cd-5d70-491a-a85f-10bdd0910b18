<template>
  <div
    class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5"
  >
    <uiCard
      v-for="(account, index) in getAccounts(searchValue)"
      :id="account?.name"
      :key="index"
      :loading="false"
      :name="account?.name"
      :logo="account?.logo || HOTELINKING_LOGO"
      data-test="brands-cards"
      @click="redirectToBrandView('brands', account.id)"
    />
    <uiCard
      v-for="(item, index) in new Array(8)"
      v-show="!brands?.length"
      :key="item + index"
      data-test="brands-skeleton-cards"
    />
  </div>
</template>
<script setup lang="ts">
import { HOTELINKING_LOGO } from "@/constants";
import { useBrandStore } from "@/stores/brand";
import { useRouter } from "vue-router";
import { onMounted, inject } from "vue";
import { storeToRefs } from "pinia";
const brandStore = useBrandStore();

const { brands, getAccounts } = storeToRefs(brandStore);
const router = useRouter();
const searchValue = inject("searchValue") as string | null;

onMounted(async (): Promise<void> => {
  !brands.value.length ? await brandStore.get() : null;
  const accounts = getAccounts.value(null);
  if (accounts && accounts.length === 1) {
  redirectToBrandView("brands", accounts[0].id);
  }
});

const redirectToBrandView = (name: string, accountId: number): void => {
  router.push({ name, params: { account_id: accountId } });
};
</script>
