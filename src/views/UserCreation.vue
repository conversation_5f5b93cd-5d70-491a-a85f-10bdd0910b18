<template>
  <div class="">
    <h3 class="text-lg font-medium leading-6 text-gray-900">
      {{ t("userCreateView.usersCreateTitle") }}
    </h3>
    <p class="mt-1 text-sm text-gray-500 mb-4">
      {{ t("userCreateView.usersCreateText") }}
    </p>
    <div class="pb-4 w-1/3">
      <uiInput
        v-model="email"
        :loading="false"
        class="inputField"
        type="email"
        :label="t('userCreateView.emailLabel')"
        :placeholder="t('userCreateView.emailPlaceholder')"
        :color="v$.email.$errors[0]?.$uid ? 'red' : ''"
        :error="v$.email.$errors[0]?.$uid ? t('auth.emailInputError') : ''"
      />
    </div>
    <div class="pb-4 w-1/3">
      <uiInput
        v-model="brands"
        :loading="false"
        class="inputField"
        :label="t('userCreateView.brandsLabel')"
        :placeholder="t('userCreateView.brandsPlaceholder')"
      />
    </div>
    <div class="mb-4 w-1/3">
      <uiSelect_v2
        v-model="selectRole"
        :loading="false"
        class="inputField"
        :label="t('userCreateView.roleLabel')"
        :color="v$.role.$errors[0]?.$uid ? 'red' : ''"
        :error="v$.role.$errors[0]?.$uid ? t('userCreateView.groupsError') : ''"
        :items="[
          { name: t('userCreateView.roles.admin'), id: 'admin' },
          { name: t('userCreateView.roles.brandAdmin'), id: 'brand_admin' },
          { name: t('userCreateView.roles.reception'), id: 'reception' },
        ]"
      />
    </div>
    <uiToggle
      class="inputField"
      :item="{ title: t('userCreateView.mfaLabel'), active: false }"
      :loading="false"
      @toggleChanged="(event) => (mfa = event.active)"
    />
    <div class="pt-5">
      <uiButton :loading="false" @click="createUser">{{
        t("common.createUser")
      }}</uiButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { required, email as validateEmail } from "@vuelidate/validators";
import { useUserListStore } from "@/stores/userList";
import { t } from "@/locales";
import { useRouter } from "vue-router";
import { useNotificationStore } from "@/stores/notification";
const notificationStore = useNotificationStore();
const router = useRouter();
import { useVuelidate } from "@vuelidate/core";

const userListStore = useUserListStore();
let email = ref("");
let brands = ref("");
let role = ref("");
let mfa = ref(false);

let selectRole = ref<{
  name: string;
  id: string;
}>({
  name: "",
  id: "",
});

let createdUserInfo = ref({
  email: "",
  brands: "",
  role: "",
});

// Validation rules
const rules = computed(() => {
  return {
    email: { required, validateEmail },
    role: { required },
  };
});

const v$ = useVuelidate(rules, {
  email,
  role,
});

async function createUser() {
  v$.value.$touch();
  if (v$.value.$error) {
    return;
  }
  try {
    const user = await userListStore.createUser(
      email.value,
      role.value,
      brands.value,
      mfa.value
    );
    if (user) {
      router.push({ name: "userManagement" });
      notificationStore.success(
        t("userCreateView.success.title"),
        t("userCreateView.success.body")
      );
    }
  } catch (error) {
    console.log(error);
  }
}
</script>
