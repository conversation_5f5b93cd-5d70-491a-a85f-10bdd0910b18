<template>
  <uiSidebarV2
    :navigation="sidebar"
    :loading="sidebar.length ? false : true"
    :sidebarOpen="true"
    @sidebarToggle="() => {}"
    @side-bar-click="sidebarActions"
  />
</template>

<script setup lang="ts">
import { brandsSideBarList } from "@/composables/sideBarLists";
import { useSidebar } from "@/composables/useSidebar";
const { sidebar, sidebarActions } = useSidebar(brandsSideBarList);
</script>
