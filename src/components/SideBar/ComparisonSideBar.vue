<template>
  <uiSidebarV2
    :navigation="sidebar"
    :loading="sidebar.length ? false : true"
    :sidebarOpen="true"
    @sidebarToggle="() => {}"
    @side-bar-click="sidebarActions"
  />
</template>
<script setup lang="ts">
import { useSidebar } from "@/composables/useSidebar";
import { accountsSideBarList } from "@/composables/sideBarLists";
const { sidebar, sidebarActions } = useSidebar(accountsSideBarList);
</script>
