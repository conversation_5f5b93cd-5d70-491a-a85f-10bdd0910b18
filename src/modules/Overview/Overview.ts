import { API } from "@aws-amplify/api";
import { format, subDays } from "date-fns";
import { calculateDaysBefore, getCurrentDateInUTC } from "@/helpers/dates";
import { isAbsoluteDateRange } from "@/helpers/uiDateRange";
import type { GetOverviewParams, ApiResponse } from "@/types";

// Formatting of props is based on the documentation of apexcharts https://apexcharts.com/docs/series/ and not on storybook.
interface QueryParams {
	interval?: string;
	date_from?: string;
	date_to?: string;
	previous_date_from?: string;
	event: string[] | null;
	name?: string;
}

export default async function get({
	brand_id,
	name,
	range,
	isReceptionModeEnabled,
}: GetOverviewParams): Promise<ApiResponse> {
	const currentDateUTC = getCurrentDateInUTC();
	let dateTo = format(currentDateUTC, "yyyy-MM-dd HH:mm:ss"); //by default, set dateTo with current hour
	let dateFrom;
	let previousDateFrom;
	const { interval, days_before } = calculateDaysBefore(range);

	//in case of absolute date range selected
	if (isAbsoluteDateRange(range)) {
		//no need to calculate dateFrom
		dateFrom = range.from;

		//if dateTo selected is not current date, modify the hours in order to retrieve data from complete day
		const currentDate = format(new Date(), "yyyy-MM-dd");
		if (range.to !== currentDate) dateTo = `${range.to} 23:59:59`;

		//calculate previousDateFrom based on the selected dateFrom and number of days included in the selected range
		const fromObject = new Date(dateFrom);
		previousDateFrom = format(
			subDays(fromObject, days_before - 1),
			"yyyy-MM-dd",
		);
	} else {
		//in case of relative date range selected, calculate dateFrom and previousDateFrom based on current date and relative date range selected
		const dateFormat = days_before === 1 ? "yyyy-MM-dd HH:mm:ss" : "yyyy-MM-dd";
		dateFrom = format(subDays(currentDateUTC, days_before), dateFormat);
		previousDateFrom = format(
			subDays(currentDateUTC, days_before * 2),
			dateFormat,
		);
	}

	const queryParams: QueryParams = {
		interval,
		date_from: dateFrom,
		date_to: dateTo,
		previous_date_from: previousDateFrom,
		event: null,
	};

	const requests = ["reservation", "scan", "checkin"];
	const funnels = ["guest_funnel", "search_funnel"];

	if (name) {
		if (requests.includes(name)) {
			queryParams.event = ["request"];
			queryParams.name = name;
		} else if (funnels.includes(name)) {
			queryParams.event = ["pageView"];
		} else {
			queryParams.event = ["checkinSource"];
		}
	} else {
		if (isReceptionModeEnabled) {
			queryParams.event = ["request", "pageView", "checkinSource"];
		} else {
			queryParams.event = ["request", "pageView"];
		}
	}
	try {
		const response = await API.get(
			"stats",
			`autocheckin/brands/${brand_id}/stats/overview`,
			{
				queryStringParameters: queryParams,
			},
		);

		return response.data;
	} catch (error) {
		console.error(error);
		throw new Error("Failed to fetch overview");
	}
}
