<script setup lang="ts">
import { t } from "@/locales";
import { useBreadcrumbs } from "@/composables/useBreadcrumbs";
import { useSidebar } from "@/composables/useSidebar";
import { useRoute } from "vue-router";
import { useRouteQuery } from "@vueuse/router";
import { computed, provide } from "vue";
const { breadcrumbs } = useBreadcrumbs();
import { brandsSideBarList } from "@/composables/sideBarLists";
const { sidebarActions } = useSidebar(brandsSideBarList);
const searchValue = useRouteQuery("search_value", "", { transform: String });

const route = useRoute();

const pageTitle = computed(() => {
  return t(`title.${String(route.name)}`);
});

provide("searchValue", searchValue);
</script>

<template>
  <div class="flex flex-col h-full">
    <RouterView name="topBar"></RouterView>
    <div class="p-6 sm:px-6 lg:px-8 flex flex-row mt-4 grow">
      <aside class="hidden lg:block lg:w-1/4 xl:w-2/12 pr-12 xl:pr-8 3xl:pr-20">
        <RouterView name="sideBar"></RouterView>
      </aside>
      <main class="flex flex-col w-full lg:w-3/4 xl:w-10/12 grow">
        <header class="mb-12">
          <div class="mx-auto flex flex-col">
            <div class="hidden sm:flex mb-6">
              <uiBreadcrumbs
                :pages="breadcrumbs"
                :loading="breadcrumbs ? false : true"
                @bread-crumb-clicked="sidebarActions"
              />
            </div>
            <router-view name="header"></router-view>
            <slot name="header"></slot>
            <h1
              class="text-2xl font-bold leading-7 sm:text-4xl sm:truncate"
              data-test="page-title"
            >
              {{ pageTitle }}
            </h1>
          </div>
        </header>
        <router-view></router-view>
      </main>
    </div>
    <uiFooter />
  </div>
</template>
